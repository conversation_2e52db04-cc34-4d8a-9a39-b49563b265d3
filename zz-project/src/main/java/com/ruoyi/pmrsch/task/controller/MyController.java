package com.ruoyi.pmrsch.task.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.ShiroUtils;
import com.ruoyi.pmrsch.pmdata.domain.ProjectInfo;
import com.ruoyi.pmrsch.task.domain.ProjectTasks;
import com.ruoyi.pmrsch.task.service.IProjectTasksService;
import com.ruoyi.pmrsch.task.service.MyProjectService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目阶段任务Controller
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Controller
@RequestMapping("/task/my")
public class MyController extends BaseController{
    private String prefix = "task/my";

    @Autowired
    private MyProjectService myProjectService;
    @Autowired
    private IProjectTasksService projectTasksService;

    /**
     * 我的项目列表页
     * @return
     */
    @RequiresPermissions("task:my:view")
    @GetMapping("")
    public String appyList(){
        return prefix + "/my";
    }

    /**
     * 我的项目列表数据
     */
    @RequiresPermissions("task:my:list")
    @PostMapping("list")
    @ResponseBody
    public TableDataInfo appyList(ProjectInfo projectInfo){
        projectInfo.setUserId(ShiroUtils.getUserId());
        startPage();
        List<ProjectInfo> list = myProjectService.getMyProjects(projectInfo);
        return getDataTable(list);
    }

    /**
     * 根据账单生产决算资料
     */
    @RequiresPermissions("task:my:gen-settle")
    @PostMapping("gen-settle")
    @ResponseBody
    public AjaxResult genSettle(String taskId){
         myProjectService.genSettleByExpertBill(taskId);
        return AjaxResult.success("生成决算资料成功");
    }

    /**
     * 查看详情
     */
    @GetMapping("{id}/view")
    public String view(@PathVariable("id") String id, ModelMap mmap){
        ProjectTasks projectTasks = projectTasksService.selectProjectTasksById(id);
        mmap.put("projectTasks", projectTasks);
        return prefix + "/view";
    }
}
