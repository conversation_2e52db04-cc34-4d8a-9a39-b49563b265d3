<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.pmrsch.task.mapper.ExpertConsultationMapper">

    <resultMap type="ExpertConsultation" id="ExpertConsultationResult">
        <result property="id"    column="id"    />
        <result property="projectTaskId"    column="project_task_id"    />
        <result property="consultationDate"    column="consultation_date"    />
        <result property="consultationTime"    column="consultation_time"    />
        <result property="projectInfoId"    column="project_info_id"    />
        <result property="expertInfoId"    column="expert_info_id"    />
        <result property="sampleTypeCode"    column="sample_type_code"    />
        <result property="consultationDuration"    column="consultation_duration"    />
        <result property="unitPrice"    column="unit_price"    />
        <result property="channel"    column="channel"    />
        <result property="channelPrice"    column="channel_price"    />
        <result property="channelAccount"    column="channel_account"    />
        <result property="question"    column="question"    />
        <result property="department"    column="department"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactEmail"    column="contact_email"    />
        <result property="remarks"    column="remarks"    />
        <result property="invoiceStatusCode"    column="invoice_status_code"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="supervisorName"    column="supervisor_name"    />
        <result property="projectTypeLabel" column="p_type_label" />
        <result property="expertCompanyName" column="company_name" />
    </resultMap>

    <resultMap type="ExpertConsultationBilling" id="ExpertConsultationBillingResult">
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="revenue"    column="revenue"    />
        <result property="channelCost"    column="channel_cost"    />
        <result property="channelCostRatio"    column="channel_cost_ratio"    />
    </resultMap>

    <sql id="selectExpertConsultationVo">
        select t.id, t.project_task_id, t.consultation_date, t.consultation_time, t.project_info_id, t.expert_info_id, t.sample_type_code, t.consultation_duration, t.unit_price, t.channel, t.channel_price, t.channel_account, t.question, t.department, t.contact_person, t.contact_email, t.remarks, t.invoice_status_code, t.created_at, t.updated_at, t.user_id, t.dept_id, u.user_name as supervisor_name,
        (
            SELECT GROUP_CONCAT(s.dict_label)
            FROM sys_dict_data s
            WHERE s.dict_type = 'zzpm_p_type'
              AND FIND_IN_SET(s.dict_value, pi.p_type_code)
        ) AS p_type_label,
        ei.company_name as company_name
        from zz_expert_consultation t
        left join zz_project_tasks pt on t.project_task_id = pt.id
        left join zz_project_info pi on pt.project_info_id = pi.id
        left join zz_customer c on pi.customer_id = c.id
        left join sys_user u on t.user_id = u.user_id
        left join zz_expert_info ei on t.expert_info_id = ei.id
    </sql>

    <select id="selectExpertConsultationList" parameterType="ExpertConsultation" resultMap="ExpertConsultationResult">
        <include refid="selectExpertConsultationVo"/>
        <where>
            <if test="projectTaskId != null  and projectTaskId != ''"> and t.project_task_id = #{projectTaskId}</if>
            <if test="consultationDate != null "> and t.consultation_date = #{consultationDate}</if>
            <if test="params.beginConsultationDate != null and params.beginConsultationDate != ''">
                and date_format(t.consultation_date,'%Y-%m-%d') &gt;= date_format(#{params.beginConsultationDate},'%Y-%m-%d')
            </if>
            <if test="params.endConsultationDate != null and params.endConsultationDate != ''">
                and date_format(t.consultation_date,'%Y-%m-%d') &lt;= date_format(#{params.endConsultationDate},'%Y-%m-%d')
            </if>
            <if test="params.customerName != null and params.customerName != ''">
                and c.name like concat('%', #{params.customerName}, '%')
            </if>
            <if test="params.supervisorName != null and params.supervisorName != ''">
                and u.user_name like concat('%', #{params.supervisorName}, '%')
            </if>
            <if test="consultationTime != null "> and t.consultation_time = #{consultationTime}</if>
            <if test="projectInfoId != null  and projectInfoId != ''"> and t.project_info_id = #{projectInfoId}</if>
            <if test="projectNo != null and projectNo != ''">
                and pi.p_no like concat('%', #{projectNo}, '%')
            </if>
            <if test="params.pName != null and params.pName != ''">
                and pi.p_name like concat('%', #{params.pName}, '%')
            </if>
            <if test="params.ofMouth != null and params.ofMouth != ''">
                and pi.of_mouth like concat('%', #{params.ofMouth}, '%')
            </if>
            <if test="expertInfoId != null  and expertInfoId != ''"> and t.expert_info_id = #{expertInfoId}</if>
            <if test="sampleTypeCode != null and sampleTypeCode != ''"> and t.sample_type_code = #{sampleTypeCode}</if>
            <if test="consultationDuration != null "> and t.consultation_duration = #{consultationDuration}</if>
            <if test="unitPrice != null"> and t.unit_price = #{unitPrice}</if>
            <if test="channel != null and channel != ''"> and t.channel = #{channel}</if>
            <if test="channelPrice != null"> and t.channel_price = #{channelPrice}</if>
            <if test="question != null  and question != ''"> and t.question = #{question}</if>
            <if test="department != null  and department != ''"> and t.department = #{department}</if>
            <if test="contactPerson != null  and contactPerson != ''"> and t.contact_person = #{contactPerson}</if>
            <if test="contactEmail != null  and contactEmail != ''"> and t.contact_email = #{contactEmail}</if>
            <if test="remarks != null  and remarks != ''"> and t.remarks = #{remarks}</if>
            <if test="invoiceStatusCode != null "> and t.invoice_status_code = #{invoiceStatusCode}</if>
            <if test="userId != null"> and t.user_id = #{userId}</if>
            <if test="deptId != null"> and t.dept_id = #{deptId}</if>
            <if test="params.beginCreatedAt != null and params.beginCreatedAt != ''">
                and date_format(t.created_at,'%Y-%m-%d') &gt;= date_format(#{params.beginCreatedAt},'%Y-%m-%d')
            </if>
            <if test="params.endCreatedAt != null and params.endCreatedAt != ''">
                and date_format(t.created_at,'%Y-%m-%d') &lt;= date_format(#{params.endCreatedAt},'%Y-%m-%d')
            </if>
            ${params.dataScope}
        </where>
    </select>

    <select id="selectExpertConsultationBillingList" parameterType="ExpertConsultationBilling" resultMap="ExpertConsultationBillingResult">
        SELECT
            t.user_id,
            u.user_name,
            SUM((if(sample_type_code = '0',t.unit_price / 60,t.unit_price)) * t.consultation_duration) AS revenue,
            SUM((if(sample_type_code = '0',t.channel_price / 60,t.channel_price)) * t.consultation_duration) AS channel_cost,
            CASE
                WHEN SUM((t.unit_price / 60) * t.consultation_duration) > 0
                THEN (SUM((t.channel_price / 60) * t.consultation_duration) / SUM((t.unit_price / 60) * t.consultation_duration)) * 100
                ELSE 0
            END AS channel_cost_ratio
        FROM
            zz_expert_consultation t
        LEFT JOIN
            sys_user u ON t.user_id = u.user_id
        <where>
            <if test="userId != null"> and t.user_id = #{userId}</if>
            <if test="beginTime != null"> and date_format(t.created_at,'%Y-%m-%d') &gt;= date_format(#{beginTime},'%Y-%m-%d')</if>
            <if test="endTime != null"> and date_format(t.created_at,'%Y-%m-%d') &lt;= date_format(#{endTime},'%Y-%m-%d')</if>
            <if test="userName != null and userName != ''"> and u.user_name like concat('%', #{userName}, '%')</if>
            ${params.dataScope}
        </where>
        GROUP BY
            t.user_id, u.user_name
        ORDER BY
            revenue DESC
    </select>

    <select id="selectExpertConsultationById" parameterType="Long" resultMap="ExpertConsultationResult">
        <include refid="selectExpertConsultationVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertExpertConsultation" parameterType="ExpertConsultation" useGeneratedKeys="true" keyProperty="id">
        insert into zz_expert_consultation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectTaskId != null">project_task_id,</if>
            <if test="consultationDate != null">consultation_date,</if>
            <if test="consultationTime != null">consultation_time,</if>
            <if test="projectInfoId != null">project_info_id,</if>
            <if test="expertInfoId != null">expert_info_id,</if>
            <if test="sampleTypeCode != null">sample_type_code,</if>
            <if test="consultationDuration != null">consultation_duration,</if>
            <if test="unitPrice != null">unit_price,</if>
            <if test="channel != null">channel,</if>
            <if test="channelPrice != null">channel_price,</if>
            <if test="channelAccount != null">channel_account,</if>
            <if test="question != null">question,</if>
            <if test="department != null">department,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactEmail != null">contact_email,</if>
            <if test="remarks != null">remarks,</if>
            <if test="invoiceStatusCode != null">invoice_status_code,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectTaskId != null">#{projectTaskId},</if>
            <if test="consultationDate != null">#{consultationDate},</if>
            <if test="consultationTime != null">#{consultationTime},</if>
            <if test="projectInfoId != null">#{projectInfoId},</if>
            <if test="expertInfoId != null">#{expertInfoId},</if>
            <if test="sampleTypeCode != null">#{sampleTypeCode},</if>
            <if test="consultationDuration != null">#{consultationDuration},</if>
            <if test="unitPrice != null">#{unitPrice},</if>
            <if test="channel != null">#{channel},</if>
            <if test="channelPrice != null">#{channelPrice},</if>
            <if test="channelAccount != null">#{channelAccount},</if>
            <if test="question != null">#{question},</if>
            <if test="department != null">#{department},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactEmail != null">#{contactEmail},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="invoiceStatusCode != null">#{invoiceStatusCode},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateExpertConsultation" parameterType="ExpertConsultation">
        update zz_expert_consultation
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectTaskId != null">project_task_id = #{projectTaskId},</if>
            <if test="consultationDate != null">consultation_date = #{consultationDate},</if>
            <if test="consultationTime != null">consultation_time = #{consultationTime},</if>
            <if test="projectInfoId != null">project_info_id = #{projectInfoId},</if>
            <if test="expertInfoId != null">expert_info_id = #{expertInfoId},</if>
            <if test="sampleTypeCode != null">sample_type_code = #{sampleTypeCode},</if>
            <if test="consultationDuration != null">consultation_duration = #{consultationDuration},</if>
            <if test="unitPrice != null">unit_price = #{unitPrice},</if>
            <if test="channel != null">channel = #{channel},</if>
            <if test="channelPrice != null">channel_price = #{channelPrice},</if>
            <if test="channelAccount != null">channel_account = #{channelAccount},</if>
            <if test="question != null">question = #{question},</if>
            <if test="department != null">department = #{department},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactEmail != null">contact_email = #{contactEmail},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="invoiceStatusCode != null">invoice_status_code = #{invoiceStatusCode},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteExpertConsultationById" parameterType="Long">
        delete from zz_expert_consultation where id = #{id}
    </delete>

    <delete id="deleteExpertConsultationByIds" parameterType="String">
        delete from zz_expert_consultation where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>