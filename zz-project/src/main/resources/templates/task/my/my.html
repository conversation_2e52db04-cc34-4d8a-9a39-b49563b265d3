<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('项目信息列表')" />
    <style>
        .task-container {
            font-family: Arial, sans-serif;
            margin: 5px;
            line-height: 1.6;
        }
        .task-phase {
            margin-bottom: 30px;
            border-left: 3px solid #eee;
            padding-left: 15px;
        }
        .phase-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .phase-status {
            display: inline-block;
            margin-left: 10px;
            font-size: 14px;
            padding: 2px 8px;
            border-radius: 3px;
        }
        .task-item {
            margin: 8px 0;
            padding-left: 5px;
            <!-- border-left: 2px solid #ddd; -->
        }
        .stats {
            color: #666;
            margin-top: 5px;
        }
        .button {
            display: flex;
        }
        .item-title { color: #000; }
        .col-sm-12 li{list-style: none;margin-top: 5px}
        .status-pending { background-color: #ffd700; color: #000; }
        .status-inprogress { background-color: #87cefa; color: #000; }
        .status-completed { background-color: #98fb98; color: #000; }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>项目编号：</label>
                            <input type="text" name="pNo"/>
                        </li>
                        <li>
                            <label>项目名称：</label>
                            <input type="text" name="pName"/>
                        </li>
                        <li>
                            <label>客户项目编号：</label>
                            <input type="text" name="pNoCus"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="pmdata:projectinfo:add">
                <i class="fa fa-plus"></i> 新增项目
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('pmdata:projectinfo:edit')}]];
    var removeFlag = [[${@permission.hasPermi('pmdata:projectinfo:remove')}]];
    var pTypeCodeDatas = [[${@dict.getType('zzpm_p_type')}]];
    var prefix = ctx + "task/my";

    $(function() {
        var options = {
            url: prefix + "/list?orderByColumn=created_at desc",
            createUrl: ctx + "pmdata/projectinfo/add",
            updateUrl: ctx + "pmdata/projectinfo/edit/{id}",
            removeUrl: ctx + "pmdata/projectinfo/remove",
            exportUrl: ctx + "pmdata/projectinfo/export",
            modalName: "项目信息",
            columns: [{
                checkbox: true
            },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'pNo',
                    title: '项目编号',
                    width: '100px'
                },
                {
                    field: 'pInfo',
                    title: '项目信息',
                    formatter: function(value, row, index) {
                        var html = '';
                        html += '<span class="item-title">项目名称:</span><br>&nbsp;&nbsp;&nbsp;&nbsp;' + row.pName + '<br><br>';
                        html += '<span class="item-title">客户名称:</span><br>&nbsp;&nbsp;&nbsp;&nbsp;' + row.pNo + '<br><br>';
                        html += '<span class="item-title">客户项目编号:</span><br>&nbsp;&nbsp;&nbsp;&nbsp;' + row.pNoCus + '<br><br>';
                        html += '<span class="item-title">项目类型:</span><br>&nbsp;&nbsp;&nbsp;&nbsp;' + $.table.selectDictLabels(pTypeCodeDatas, row.pTypeCode) + '<br><br>';
                        html += '<span class="item-title">项目备注:</span><br>&nbsp;&nbsp;&nbsp;&nbsp;' + row.remarks;
                        return html;
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<div class="col-sm-12" style="padding-bottom: 6px !important;"><ul>')
                        actions.push('<li><a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.modal.openFull(\''+$.common.sprintf("项目[%s] -> 新增任务",row.pName)+'\',\''+$.common.sprintf("%stask/apply/add?projectInfoId=%s",ctx,row.id)+'\')"><i class="fa fa-edit"></i>新增任务</a> </li>');
                        actions.push('<li><a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑项目</a> </li>');
                        actions.push('<li><a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除项目</a></li>');
                        actions.push('</ul></div>')
                        return actions.join('');
                    }
                },
                {
                    field: 'task',
                    align: 'left',
                    title: '任务信息',
                    formatter: function (value, row, index) {

                        if(!row.taskTotalList){
                            return '';
                        }

                        var html = '';
                        html +='<div class="task-container">';

                        for(var total in row.taskTotalList){
                            var btnBudget = row.taskTotalList[total].btnBudget ? '' : 'hidden';
                            var btnCommitTask = row.taskTotalList[total].btnCommitTask ? '' : 'hidden';
                            var btnEditTask = row.taskTotalList[total].btnEditTask ? '' : 'hidden';
                            var btnDelTask = row.taskTotalList[total].btnDelTask ? '' : 'hidden';

                            var btnApplySettle = row.taskTotalList[total].btnApplySettle ? '' : 'hidden';
                            var btnCommitSettle = row.taskTotalList[total].btnCommitSettle ? '' : 'hidden';
                            var btnLabor = row.taskTotalList[total].btnLabor ? '' : 'hidden';
                            var btnRespondent = row.taskTotalList[total].btnRespondent ? '' : 'hidden';

                            html += '   <div class="task-phase">';
                            html += '       <div class="phase-title">';
                            html += '           ' + row.taskTotalList[total].taskName;
                            html += '           <span class="phase-status status-pending">'+row.taskTotalList[total].taskStatus+'</span>';
                            html += '           <a href="javascript:void(0)" onclick="$.modal.openFull(\''+$.common.sprintf("项目[%s] -> 任务[%s] -> 详情",row.pName,row.taskTotalList[total].taskName)+'\',\''+$.common.sprintf("%s/%s/view",ctx+"task/my",row.taskTotalList[total].id)+'\')"><i class="fa fa-info-circle"></i> 查看详情</a>';
                            html += '       </div>';

                            html += '       <div class="task-item">';
                            html += $.common.sprintf('<span class="item-title">预算:</span>%s个 待提交(%s) 审核中(%s) 通过(%s) 不通过(%s)，预算总额：<strong>%s</strong>',row.taskTotalList[total].budgetNum,row.taskTotalList[total].budgetNumWaitSubmit,row.taskTotalList[total].budgetNumReviewing,row.taskTotalList[total].budgetNumPass,row.taskTotalList[total].budgetNumReject,row.taskTotalList[total].budgetTotal);
                            html += '       </div>';

                            html += '       <div class="task-item">';
                            html += $.common.sprintf('<span class="item-title">借款:</span>%s个 待提交(%s) 审核中(%s) 通过(%s) 不通过(%s)，借款总额：<strong>%s</strong>',row.taskTotalList[total].loansNum,row.taskTotalList[total].loansNumWaitSubmit,row.taskTotalList[total].loansNumReviewing,row.taskTotalList[total].loansNumPass,row.taskTotalList[total].loansNumReject,row.taskTotalList[total].totalLoans);
                            html += '       </div>';

                            html += '       <div class="task-item stats">';
                            html += $.common.sprintf('<span class="item-title">统计:</span>合同总额(%s), 预算总额(%s), 初步预算比例(%s%), 初步成本比例(%s%)',row.taskTotalList[total].contractTotal,row.taskTotalList[total].budgetTotal,row.taskTotalList[total].budgetRatio,row.taskTotalList[total].costRatio);
                            html += '       </div>';

                            html += '       <div class="task-item button">';

                            html += '<div class="select-list"><ul>';
                            html += '<li><a class="btn btn-info btn-xs ' + btnEditTask + '" href="javascript:void(0)" onclick="$.modal.openFull(\''+$.common.sprintf("项目[%s] -> 修改任务",row.pName)+'\',\''+$.common.sprintf("%stask/apply/edit/%s",ctx,row.taskTotalList[total].id)+'\')"><i class="fa fa-edit"></i>修改任务</a> </li>';
                            html += '<li><a class="btn btn-primary btn-xs ' + btnCommitTask + '" href="javascript:void(0)" onclick="genSettle(\'' + row.taskTotalList[total].id + '\')"><i class="fa fa-edit"></i>生成决算</a> </li>';
                            html += '<li><a class="btn btn-primary btn-xs ' + btnCommitTask + '" href="javascript:void(0)" onclick="submitReview(\'' + row.taskTotalList[total].id + '\')"><i class="fa fa-edit"></i>任务提审</a> </li>';
                            html += '<li><a class="btn btn-danger btn-xs ' + btnDelTask + '" href="javascript:void(0)" onclick="delTask(\'' + row.taskTotalList[total].id + '\')"><i class="fa fa-remove"></i>删除任务</a> </li>';
                            html += '<li><a class="btn btn-warning btn-xs ' + btnBudget + '" href="javascript:void(0)" onclick="$.modal.openTab(\'预算/借款\',\''+$.common.sprintf("%stask/budget/apply?projectTaskId=%s",ctx,row.taskTotalList[total].id)+'\')"><i class="fa fa-edit"></i>预算/借款</a> </li>';
                            html += '</ul></div>';

                            html += '<div class="select-list"><ul>';
                            html += '<li><a class="btn btn-info btn-xs ' + btnApplySettle + '" href="javascript:void(0)" onclick="$.modal.openFull(\''+$.common.sprintf("项目[%s] -> 任务[%s] -> 决算申请",row.pName,row.taskTotalList[total].taskName)+'\',\''+$.common.sprintf("%stask/settle/apply/edit/%s",ctx,row.taskTotalList[total].id)+'\')"><i class="fa fa-edit"></i>填写决算</a> </li>';

                            if (row.taskTotalList[total].btnLabor) {
                                html += '<li><a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="manageLabor(\''+$.common.sprintf("项目[%s] -> 任务[%s] -> 劳务信息",row.pName,row.taskTotalList[total].taskName)+'\',\''+row.taskTotalList[total].id+'\',false)"><i class="fa fa-edit"></i>填写劳务</a> </li>';
                            }

                            if (!row.taskTotalList[total].btnLabor && row.taskTotalList[total].isBudgetPass) {
                                html += '<li><a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="manageLabor(\''+$.common.sprintf("项目[%s] -> 任务[%s] -> 劳务信息",row.pName,row.taskTotalList[total].taskName)+'\',\''+row.taskTotalList[total].id+'\',true)"><i class="fa fa-eye"></i>查看劳务</a> </li>';
                            }

                            if (row.taskTotalList[total].btnRespondent) {
                                html += '<li><a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="manageRespondent(\''+$.common.sprintf("项目[%s] -> 任务[%s] -> 受访信息",row.pName,row.taskTotalList[total].taskName)+'\',\''+row.taskTotalList[total].id+'\',false)"><i class="fa fa-edit"></i>填写受访</a> </li>';
                            }

                            if (!row.taskTotalList[total].btnRespondent && row.taskTotalList[total].isBudgetPass) {
                                html += '<li><a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="manageRespondent(\''+$.common.sprintf("项目[%s] -> 任务[%s] -> 受访信息",row.pName,row.taskTotalList[total].taskName)+'\',\''+row.taskTotalList[total].id+'\',true)"><i class="fa fa-eye"></i>查看受访</a> </li>';
                            }

                            html += '<li><a class="btn btn-primary btn-xs ' + btnCommitSettle + '" href="javascript:void(0)" onclick="submitReviewSettle(\'' + row.taskTotalList[total].id + '\')"><i class="fa fa-edit"></i>决算提审</a> </li>';
                            html += '</ul></div>';

                            html += '       </div>';

                            html += '   </div>';
                        }

                        html += '</div>';

                        return html;
                    },
                }]
        };
        $.table.init(options);
    });

    function delTask(id) {
        $.modal.confirm("确定要删除该项目任务吗？", function() {
            $.operate.post(ctx + "task/apply/remove?ids="+id);
        })
    }

    function submitReview(id) {
        $.modal.confirm("确定要提交审核吗？", function() {
            $.operate.post(ctx + "task/"+id+"/review?review=1");
        })
    }

    function manageLabor(title, id, isView){
        if(isView){
            var options = {
                title: title,
                width: "100%",
                height: "100%",
                url: ctx + "task/labor?isView=1&projectTaskId=" + id,
                btn: 0,
                full: true
            };
            $.modal.openOptions(options);
        }else{
            $.modal.openTab(title, ctx + "task/labor?projectTaskId=" + id);
        }
    }

    function manageRespondent(title, id, isView){
        if(isView){
            var options = {
                title: title,
                width: "100%",
                height: "100%",
                url: ctx + "task/respondent?isView=1&projectTaskId=" + id,
                btn: 0,
                full: true
            };
            $.modal.openOptions(options);
        }else{
            $.modal.openTab(title, ctx + "task/respondent?projectTaskId=" + id);
        }
    }

    function submitReviewSettle(id) {
        $.modal.confirm("确定要提交审核吗？", function() {
            $.operate.post(ctx + "task/settle/"+id+"/review?review=1");
        })
    }

    function genSettle(id) {
        $.modal.confirm("将根据专家账单信息生成决算资料，且会删除现有的决算资料，是否确定操作？", function() {
            $.operate.post(ctx + "task/my/gen-settle?taskId=" + id);
        })
    }
</script>
</body>
</html>