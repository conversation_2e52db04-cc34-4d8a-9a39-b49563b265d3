<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增专家咨询记录')" />
    <th:block th:include="include :: datetimepicker-css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-timepicker/0.5.2/css/bootstrap-timepicker.min.css" rel="stylesheet">
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-consultation-add">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">项目任务：</label>
                    <div class="col-sm-8">
                        <div class="input-group">
                            <input id="projectTaskId" name="projectTaskId" class="form-control" type="text" required>
                            <div class="input-group-btn">
                                <button type="button" class="btn btn-white dropdown-toggle" style="height: 31px" data-toggle="dropdown">
                                    <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">咨询日期：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="consultationDate" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">咨询时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group bootstrap-timepicker">
                            <input id="timeDisplay" class="form-control timepicker" placeholder="HH:mm" type="text">
                            <input name="consultationTime" id="consultationTime" type="hidden" required>
                            <span class="input-group-addon"><i class="fa fa-clock-o"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">专家：</label>
                    <div class="col-sm-8">
                        <div class="input-group">
                            <input id="expertInfoId" name="expertInfoId" class="form-control" type="text" required>
                            <div class="input-group-btn">
                                <button type="button" class="btn btn-white dropdown-toggle" style="height: 31px" data-toggle="dropdown">
                                    <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">费用类型：</label>
                    <div class="col-sm-8">
                        <select name="sampleTypeCode" class="form-control" th:with="type=${@dict.getType('zzpm_sample_type')}" required>
                            <option value="">请选择</option>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">定量数量或访谈时长(min)：</label>
                    <div class="col-sm-8">
                        <input name="consultationDuration" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">单价(RMB)：</label>
                    <div class="col-sm-8">
                        <input name="unitPrice" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">渠道：</label>
                    <div class="col-sm-8">
                        <input name="channel" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">渠道报价(RMB)：</label>
                    <div class="col-sm-8">
                        <input name="channelPrice" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">渠道收款账号：</label>
                    <div class="col-sm-8">
                        <input name="channelAccount" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">问题：</label>
                    <div class="col-sm-8">
                        <input name="question" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">部门：</label>
                    <div class="col-sm-8">
                        <input name="department" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">联系人：</label>
                    <div class="col-sm-8">
                        <input name="contactPerson" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">联系人邮箱：</label>
                    <div class="col-sm-8">
                        <input name="contactEmail" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remarks" class="form-control"></textarea>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">开票状态：</label>
                    <div class="col-sm-8">
                        <select name="invoiceStatusCode" class="form-control" th:with="type=${@dict.getType('zzpm_invoice_status')}">
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                        </select>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: bootstrap-suggest-js" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-timepicker/0.5.2/js/bootstrap-timepicker.min.js"></script>
    <script th:inline="javascript">
        var prefix = ctx + "task/consultation"
        $("#form-consultation-add").validate({
            focusCleanup: true
        });

        var projectTaskIdSuggest = $("#projectTaskId").bsSuggest({
            url: ctx + "task/projecttask/suggestList",
            showBtn: true,
            //showHeader: true,
            idField: "id",
            keyField: "taskName"
        }).on('onDataRequestSuccess', function (e, result) {
            console.log('onDataRequestSuccess: ', result);
        }).on('onSetSelectValue', function (e, keyword) {
            console.log('onSetSelectValue: ', keyword);
            // 当选择项目任务时，自动设置项目ID
            if (keyword && keyword.id) {
                $("select[name='projectInfoId']").val(keyword.projectInfoId);
            }
        }).on('onUnsetSelectValue', function (e) {
            console.log("onUnsetSelectValue");
        });

        var expertInfoSuggest = $("#expertInfoId").bsSuggest({
            url: ctx + "pmdata/expertinfo/suggestList",
            showBtn: true,
            //showHeader: true,
            idField: "id",
            keyField: "expertName"
        }).on('onDataRequestSuccess', function (e, result) {
            console.log('onDataRequestSuccess: ', result);
        }).on('onSetSelectValue', function (e, keyword) {
            console.log('onSetSelectValue: ', keyword);
        }).on('onUnsetSelectValue', function (e) {
            console.log("onUnsetSelectValue");
        });

        function submitHandler() {
            if ($.validate.form()) {
                // 确保时间值已设置
                if ($('#timeDisplay').val() && !$('#consultationTime').val()) {
                    var timeValue = $('#timeDisplay').val();
                    var timeParts = timeValue.split(':');
                    var hours = timeParts[0].padStart(2, '0');
                    var minutes = (timeParts[1] || '00').padStart(2, '0');
                    $('#consultationTime').val(hours + ':' + minutes + ':00');
                }

                $("#expertInfoId").val($("#expertInfoId").data('id'))
                $("#projectTaskId").val($("#projectTaskId").data('id'))
                $.operate.save(prefix + "/add", $('#form-consultation-add').serialize());
            }
        }

        $("input[name='consultationDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $('.timepicker').timepicker({
            showMeridian: false,
            minuteStep: 5,
            defaultTime: false,
            showInputs: true,
            showSeconds: false,
            showClose: true
        }).on('changeTime.timepicker', function(e) {
            // 当时间改变时，更新隐藏字段的值
            var hours = e.time.hours < 10 ? '0' + e.time.hours : e.time.hours;
            var minutes = e.time.minutes < 10 ? '0' + e.time.minutes : e.time.minutes;
            var timeString = hours + ':' + minutes + ':00';
            $('#consultationTime').val(timeString);
        });
    </script>
</body>
</html>